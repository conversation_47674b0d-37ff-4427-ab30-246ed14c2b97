from .base import BaseWebVisitClient
from .firecrawl import FireCrawlWebVisitClient
from ii_tool.core.config import WebVisitConfig, CompressorConfig

def create_web_visit_client(settings: WebVisitConfig, compressor_config: CompressorConfig) -> BaseWebVisitClient:
    """
    Factory function that creates a web visit client based on available API keys.
    Priority order: FireCrawl > Jina > Tavily > Markdownify

    Args:
        settings: Settings object containing API keys

    Returns:
        BaseWebVisitClient: An instance of a web visit client
    """
    firecrawl_key = settings.firecrawl_api_key

    if firecrawl_key:
        print("Using FireCrawl to visit webpage")
        return FireCrawlWebVisitClient(api_key=firecrawl_key, compressor_config=compressor_config)

    raise ValueError("No web visit API key found")