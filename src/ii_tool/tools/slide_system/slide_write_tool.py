"""Slide writing tool for creating and overwriting slides."""

from typing import Any
from pathlib import Path
from ii_tool.core.workspace import FileSystemValidationError
from ii_tool.tools.base import Too<PERSON>Result, ToolConfirmationDetails
from .base import SlideToolBase


# Name
NAME = "SlideWrite"
DISPLAY_NAME = "Write slide"

# Tool description
DESCRIPTION = """Creates or overwrites a slide with complete HTML content.

Usage:
- Use this tool instead of FileWriteTool for slide content
- Creates a new slide or overwrites an existing one in a presentation
- The slide will be saved as slide_XXX.html (e.g., slide_001.html for slide 1)
- Automatically creates the presentation directory if it doesn't exist
- Updates the metadata.json with slide information
- The HTML content should follow the system prompt guidelines for dimensions"""

# Input schema
INPUT_SCHEMA = {
    "type": "object",
    "properties": {
        "presentation_name": {
            "type": "string",
            "description": "Name of the presentation",
        },
        "slide_number": {
            "type": "integer",
            "description": "Slide number (1-based)",
            "minimum": 1,
        },
        "content": {
            "type": "string",
            "description": "Complete HTML content for the slide",
        },
        "title": {"type": "string", "description": "Slide title for metadata"},
        "description": {
            "type": "string",
            "description": "Purpose/description of the slide",
        },
        "type": {
            "type": "string",
            "description": "Slide type (cover, content, chart, conclusion, etc.)",
            "default": "content",
        },
    },
    "required": [
        "presentation_name",
        "slide_number",
        "content",
        "title",
        "description",
    ],
}


class SlideWriteTool(SlideToolBase):
    """Tool for writing content to slides."""

    name = NAME
    display_name = DISPLAY_NAME
    description = DESCRIPTION
    input_schema = INPUT_SCHEMA
    read_only = False

    def should_confirm_execute(
        self, tool_input: dict[str, Any]
    ) -> ToolConfirmationDetails | bool:
        presentation_name = tool_input.get("presentation_name", "")
        slide_number = tool_input.get("slide_number", 1)
        title = tool_input.get("title", "")

        return ToolConfirmationDetails(
            type="edit",
            message=f"Write slide {slide_number} in presentation '{presentation_name}' with title '{title}'",
        )

    async def execute(
        self,
        tool_input: dict[str, Any],
    ) -> ToolResult:
        """Execute the slide write operation."""
        presentation_name = tool_input.get("presentation_name")
        slide_number = tool_input.get("slide_number")
        content = tool_input.get("content")
        title = tool_input.get("title")
        description = tool_input.get("description")
        slide_type = tool_input.get("type", "content")

        try:
            # Get presentation path
            presentation_path = self._get_presentation_path(presentation_name)

            # Validate the path with workspace manager
            full_path = Path.cwd() / presentation_path
            self.workspace_manager.validate_path(str(full_path))

            # Create presentation directory if it doesn't exist
            presentation_path.mkdir(parents=True, exist_ok=True)

            # Load or create metadata
            metadata = self._load_metadata(presentation_path)

            # Update presentation name in metadata if empty
            if not metadata["presentation"]["name"]:
                metadata["presentation"]["name"] = presentation_name
                metadata["presentation"]["title"] = presentation_name

            # Get slide filename
            slide_filename = self._get_slide_filename(slide_number)
            slide_path = presentation_path / slide_filename

            # Check if this is a new slide or overwriting
            is_new_slide = not slide_path.exists()

            # Write slide content
            slide_path.write_text(content, encoding="utf-8")

            # Update metadata
            metadata = self._update_slide_in_metadata(
                metadata=metadata,
                slide_number=slide_number,
                title=title,
                description=description,
                slide_type=slide_type,
            )

            # Save metadata
            self._save_metadata(presentation_path, metadata)

            # Prepare success message
            total_slides = len(metadata.get("slides", []))

            if is_new_slide:
                return ToolResult(
                    llm_content=f"Successfully created slide {slide_number} in presentation '{presentation_name}'\n"
                    f"File: {slide_path}\n"
                    f"Title: {title}\n"
                    f"Total slides in presentation: {total_slides}",
                    user_display_content={"content": content},
                    is_error=False,
                )
            else:
                return ToolResult(
                    llm_content=f"Successfully overwrote slide {slide_number} in presentation '{presentation_name}'\n"
                    f"File: {slide_path}\n"
                    f"Title: {title}\n"
                    f"Total slides in presentation: {total_slides}",
                    user_display_content={"content": content},
                    is_error=False,
                )

        except FileSystemValidationError as e:
            return ToolResult(llm_content=f"ERROR: {e}", is_error=True)
        except Exception as e:
            return ToolResult(
                llm_content=f"ERROR: Failed to write slide: {str(e)}", is_error=True
            )

    async def execute_mcp_wrapper(
        self,
        presentation_name: str,
        slide_number: int,
        content: str,
        title: str,
        description: str,
        type: str = "content",
    ):
        return await self._mcp_wrapper(
            tool_input={
                "presentation_name": presentation_name,
                "slide_number": slide_number,
                "content": content,
                "title": title,
                "description": description,
                "type": type,
            }
        )
