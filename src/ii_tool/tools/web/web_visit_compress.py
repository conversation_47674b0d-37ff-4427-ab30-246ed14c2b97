import httpx
from typing import Any, List
from ii_tool.tools.base import BaseTool, ToolResult
from ii_tool.tools.constants import DEFAULT_TIMEOUT_MS

# Name
NAME = "web_visit_compress"
DISPLAY_NAME = "Web Visit Compress"

# Tool description
DESCRIPTION = "You should call this tool when you need to visit a webpage and extract relevant content. Returns relevant webpage content as text."

# Input schema
INPUT_SCHEMA = {
    "type": "object",
    "properties": {
        "urls": {
            "type": "array",
            "items": {
                "type": "string",
            },
            "description": "The urls of the webpages to visit.",
        },
        "query": {
            "type": "string",
            "description": "The query to extract relevant content.",
        },
    },
    "required": ["urls", "query"],
}


class WebVisitCompressTool(BaseTool):
    name = NAME
    display_name = DISPLAY_NAME
    description = DESCRIPTION
    input_schema = INPUT_SCHEMA
    read_only = True

    def __init__(self, tool_server_url: str):
        super().__init__()
        self.tool_server_url = tool_server_url

    async def execute(
        self,
        tool_input: dict[str, Any],
    ) -> ToolResult:
        urls = tool_input["urls"]
        query = tool_input["query"]
        process_urls = []
        for url in urls:
            if "arxiv.org/abs" in url:
                url = "https://arxiv.org/html/" + url.split("/")[-1]
            process_urls.append(url)

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.tool_server_url}/v2/web-visit-compress",
                    json={"urls": process_urls, "query": query},
                    timeout=DEFAULT_TIMEOUT_MS / 1000,
                )
                response.raise_for_status()
                response_data = response.json()
        except Exception as e:
            return ToolResult(
                llm_content="",
                user_display_content=str(e),
                is_error=True,
            )

        if not response_data["success"]:
            return ToolResult(
                llm_content="",
                user_display_content=response_data["error"],
                is_error=True,
            )

        content = response_data["content"]

        return ToolResult(
            llm_content=content,
            user_display_content=content,
        )


    async def execute_mcp_wrapper(self, urls: List[str], query: str):
        return await self._mcp_wrapper(
            tool_input={
                "urls": urls,
                "query": query,
            }
        )