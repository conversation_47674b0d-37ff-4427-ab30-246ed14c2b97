from ii_agent.sandbox.ii_sandbox import II<PERSON>andbox
from ii_tool.core.workspace import WorkspaceManager
from ii_tool.tools.dev.database import GetDatabaseConnection

from ii_tool.tools.shell import (
    ShellInit,
    ShellRunCommand,
    ShellView,
    ShellKill,
    ShellStopCommand,
    ShellList,
    TmuxSessionManager,
    ShellWriteToProcessTool,
)
from ii_tool.tools.file_system import (
    GlobTool,
    GrepTool,
    LSTool,
    FileReadTool,
    FileWriteTool,
    FileEditTool,
    MultiEditTool,
)
from ii_tool.tools.productivity import TodoReadTool, TodoWriteTool
from ii_tool.tools.media import (
    VideoGenerateTool,
    ImageGenerateTool,
)
from ii_tool.tools.dev import FullStackInitTool, RegisterPort
from ii_tool.tools.web import (
    WebSearchTool,
    WebVisitTool,
    ImageSearchTool,
    WebVisitCompressTool,
    ReadRemoteImageTool,
    WebBatchSearchTool,
)
from ii_tool.tools.slide_system.slide_edit_tool import <PERSON>lide<PERSON>dit<PERSON>ool
from ii_tool.tools.slide_system.slide_write_tool import <PERSON><PERSON><PERSON><PERSON><PERSON>ool
from ii_tool.tools.browser import (
    <PERSON>rowser<PERSON><PERSON><PERSON><PERSON>,
    <PERSON>rowser<PERSON>ait<PERSON>ool,
    <PERSON>rowserViewTool,
    <PERSON>rowserScrollDownTool,
    BrowserScrollUpTool,
    BrowserSwitchTabTool,
    BrowserOpenNewTabTool,
    BrowserGetSelectOptionsTool,
    BrowserSelectDropdownOptionTool,
    BrowserNavigationTool,
    BrowserRestartTool,
    BrowserEnterTextTool,
    BrowserPressKeyTool,
    BrowserDragTool,
    BrowserEnterMultipleTextsTool,
)
from ii_tool.browser.browser import Browser


def get_common_tools(
    sandbox: IISandbox,
):
    tools = [
        # Sandbox tools
        RegisterPort(sandbox=sandbox),
    ]

    return tools


def get_sandbox_tools(workspace_path: str, tool_server_url: str):
    terminal_manager = TmuxSessionManager()
    workspace_manager = WorkspaceManager(workspace_path)
    browser = Browser()

    tools = [
        # Shell tools
        ShellInit(terminal_manager, workspace_manager),
        ShellRunCommand(terminal_manager),
        ShellView(terminal_manager),
        ShellKill(terminal_manager),
        ShellStopCommand(terminal_manager),
        ShellList(terminal_manager),
        ShellWriteToProcessTool(terminal_manager),
        # File system tools
        GlobTool(workspace_manager),
        GrepTool(workspace_manager),
        LSTool(workspace_manager),
        FileReadTool(workspace_manager),
        FileWriteTool(workspace_manager),
        FileEditTool(workspace_manager),
        MultiEditTool(workspace_manager),
        FullStackInitTool(workspace_manager),
        # Media tools
        ImageGenerateTool(tool_server_url, workspace_manager),
        VideoGenerateTool(tool_server_url, workspace_manager),
        # Web tools
        WebSearchTool(tool_server_url),
        WebVisitTool(tool_server_url),
        WebVisitCompressTool(tool_server_url),
        ImageSearchTool(tool_server_url),
        ReadRemoteImageTool(),
        WebBatchSearchTool(tool_server_url),
        # Database tools
        GetDatabaseConnection(tool_server_url),
        # Todo tools
        TodoReadTool(),
        TodoWriteTool(),
        # Slide tools
        SlideWriteTool(workspace_manager),
        SlideEditTool(workspace_manager),
        # Browser tools
        BrowserClickTool(browser),
        BrowserWaitTool(browser),
        BrowserViewTool(browser),
        BrowserScrollDownTool(browser),
        BrowserScrollUpTool(browser),
        BrowserSwitchTabTool(browser),
        BrowserOpenNewTabTool(browser),
        BrowserGetSelectOptionsTool(browser),
        BrowserSelectDropdownOptionTool(browser),
        BrowserNavigationTool(browser),
        BrowserRestartTool(browser),
        BrowserEnterTextTool(browser),
        BrowserPressKeyTool(browser),
        BrowserDragTool(browser),
        BrowserEnterMultipleTextsTool(browser),
    ]

    return tools
