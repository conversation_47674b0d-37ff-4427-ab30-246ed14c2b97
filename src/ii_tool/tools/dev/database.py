from typing import Any

from httpx import AsyncClient
from ii_tool.tools.base import BaseTool, ToolResult
from ii_tool.tools.constants import DEFAULT_TIMEOUT_MS

# Name
NAME = "get_database_connection"
DISPLAY_NAME = "Get database connection"

# Tool description
DESCRIPTION = """Get a database connection.
- Get connection details for database operations.
- Support multiple database types (currently: postgres).
- Provide connection string for use in applications.
"""

# Input schema
INPUT_SCHEMA = {
    "type": "object",
    "properties": {
        "database_type": {
            "type": "string",
            "description": "Type of the database to connect to",
            "enum": ["postgres"],
        },
    },
    "required": ["database_type"],
}

class GetDatabaseConnection(BaseTool):
    name = NAME
    display_name = DISPLAY_NAME
    description = DESCRIPTION
    input_schema = INPUT_SCHEMA
    read_only = False

    def __init__(
        self,
        tool_server_url: str,
    ) -> None:
        super().__init__()
        self.tool_server_url = tool_server_url

    async def execute(
        self,
        tool_input: dict[str, Any],
    ) -> ToolResult:
        database_type = tool_input["database_type"]
        
        try:
            async with AsyncClient(
                base_url=self.tool_server_url,
            ) as client:
                response = await client.post(
                    f"{self.tool_server_url}/database",
                    json={
                        "database_type": database_type,
                    },
                    timeout=DEFAULT_TIMEOUT_MS / 1000,
                )
        except Exception as e:
            return ToolResult(
                llm_content=f"Failed to get database connection. Error: {str(e)}",
                user_display_content=f"Failed to get database connection. Error: {str(e)}",
                is_error=True,
            )

        if response.status_code != 200 or response.json().get("success") is False:
            return ToolResult(
                llm_content=f"Failed to get database connection. Error: {response.json().get('error')}",
                user_display_content=f"Failed to get database connection. Error: {response.json().get('error')}",
                is_error=True,
            )
        connection_string = response.json()["connection_string"]

        return ToolResult(
            llm_content=f"Successfully got database connection. Tool output: {connection_string}",
            user_display_content=f"Successfully got database connection. Tool output: {connection_string}",
            is_error=False,
        )

    async def execute_mcp_wrapper(
        self,
        database_type: str,
    ):
        return await self._mcp_wrapper(
            tool_input={
                "database_type": database_type,
            }
        )
