from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional, List
import uuid
from sqlalchemy import asc, select, desc, func
from sqlalchemy.orm import selectinload
from ii_agent.core.config.ii_agent_config import config
from ii_agent.db.models import Session, Event, FileUpload
from ii_agent.core.event import EventType, RealtimeEvent
from ii_agent.core.config.ii_agent_config import II_AGENT_DIR
from ii_agent.core.logger import logger
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.ext.asyncio import async_sessionmaker
from sqlalchemy.ext.asyncio import AsyncSession as DBSession


def run_migrations():
    try:
        from alembic import command
        from alembic.config import Config

        alembic_cfg = Config(II_AGENT_DIR / "alembic.ini")
        migrations_path = II_AGENT_DIR / "migrations"
        alembic_cfg.set_main_option("script_location", str(migrations_path))

        command.upgrade(alembic_cfg, "head")

    except Exception as e:
        logger.error(f"Error running migrations: {e}")
        raise


# run_migrations()

engine = create_async_engine(config.database_url, echo=False, future=True)
SessionLocal = async_sessionmaker(bind=engine, expire_on_commit=False)


@asynccontextmanager
async def get_db() -> AsyncGenerator[DBSession, None]:
    """Get a database session as a context manager.

    Yields:
        A database session that will be automatically committed or rolled back
    """
    async with SessionLocal() as db:
        try:
            yield db
            await db.commit()
        except Exception:
            await db.rollback()
            raise
        finally:
            await db.close()


class SessionsTable:
    """Table class for session operations following Open WebUI pattern."""

    async def create_session(
        self,
        session_uuid: uuid.UUID,
        user_id: str,
        agent_state_path: str,
        name: Optional[str] = None,
    ) -> uuid.UUID:
        """Create a new session with a UUID-based workspace directory.

        Args:
            session_uuid: The UUID for the session
            user_id: The ID of the user creating the session
            title: Optional title for the session

        Returns:
            A tuple of (session_uuid, workspace_path)
        """
        # Create session in database
        async with get_db() as db:
            db_session = Session(id=str(session_uuid), user_id=user_id, name=name, agent_state_path=agent_state_path)
            db.add(db_session)
            await db.flush()  # This will populate the id field

        return session_uuid

    async def update_sandbox_id(self, session_uuid: uuid.UUID, sandbox_id: str):
        """Update the sandbox ID for a session.

        Args:
            session_uuid: The UUID of the session
            sandbox_id: The sandbox ID
        """
        async with get_db() as db:
            db_session = await db.execute(
                select(Session).where(Session.id == str(session_uuid))
            )
            db_session = db_session.scalar_one_or_none()
            if db_session:
                db_session.sandbox_id = sandbox_id
                await db.flush()

    async def get_session_by_workspace(self, workspace_dir: str) -> Optional[Session]:
        """Get a session by its workspace directory.

        Args:
            workspace_dir: The workspace directory path

        Returns:
            The session if found, None otherwise
        """
        async with get_db() as db:
            result = await db.execute(
                select(Session).where(Session.workspace_dir == workspace_dir)
            )
            return result.scalar_one_or_none()

    async def session_has_sandbox(self, session_id: uuid.UUID) -> bool:
        """Check if a sandbox ID exists in the database.

        Args:
            session_id: The session ID to check

        Returns:
            True if the sandbox ID exists, False otherwise
        """
        async with get_db() as db:
            result = await db.execute(
                select(Session).where(Session.id == str(session_id))
            )
            session = result.scalar_one_or_none()
            return session is not None and session.sandbox_id is not None

    async def get_session_by_id(self, session_id: uuid.UUID) -> Optional[Session]:
        """Get a session by its UUID.

        Args:
            session_id: The UUID of the session

        Returns:
            The session if found, None otherwise
        """
        async with get_db() as db:
            result = await db.execute(
                select(Session).where(Session.id == str(session_id))
            )
            return result.scalar_one_or_none()

    async def update_session_name(self, session_id: uuid.UUID, name: str) -> None:
        """Update the name of a session.

        Args:
            session_id: The UUID of the session to update
            name: The new name for the session
        """
        async with get_db() as db:
            result = await db.execute(
                select(Session).where(Session.id == str(session_id))
            )
            db_session = result.scalar_one_or_none()
            if db_session:
                db_session.name = name
                await db.flush()

    async def update_session_agent_type(
        self, session_id: uuid.UUID, agent_type: str
    ) -> None:
        """Update the agent type of a session.

        Args:
            session_id: The UUID of the session to update
            agent_type: The new agent type for the session
        """
        async with get_db() as db:
            result = await db.execute(
                select(Session).where(Session.id == str(session_id))
            )
            db_session = result.scalar_one_or_none()
            if db_session:
                db_session.agent_type = agent_type
                await db.flush()

    async def get_user_sessions(
        self,
        user_id: str,
        search_term: Optional[str] = None,
        page: int = 1,
        per_page: int = 20,
    ) -> tuple[List[dict], int]:
        """Get sessions for a user with optional search and pagination.

        Args:
            user_id: The ID of the user
            search_term: Optional search term to filter sessions by name
            page: Page number for pagination (1-indexed)
            per_page: Number of items per page

        Returns:
            A tuple of (list of session dictionaries, total count)
        """
        async with get_db() as db:
            query = select(Session).where(
                Session.user_id == user_id,
                Session.deleted_at.is_(None)
            )

            if search_term:
                query = query.where(Session.name.ilike(f"%{search_term}%"))

            # Get total count
            count_query = select(func.count()).select_from(Session).where(
                Session.user_id == user_id,
                Session.deleted_at.is_(None)
            )
            if search_term:
                count_query = count_query.where(Session.name.ilike(f"%{search_term}%"))
            
            count_result = await db.execute(count_query)
            total = count_result.scalar()

            # Apply pagination
            offset = (page - 1) * per_page
            query = (
                query.order_by(desc(Session.created_at)).limit(per_page).offset(offset)
            )

            result = await db.execute(query)
            sessions = result.scalars().all()

            return [
                {
                    "id": str(session.id),
                    "user_id": session.user_id,
                    "name": session.name,
                    "status": session.status,
                    "sandbox_id": session.sandbox_id,
                    "workspace_dir": f"/workspace/{session.id}",
                    "is_public": session.is_public,
                    "public_url": session.public_url,
                    "token_usage": None,
                    "settings": None,
                    "agent_type": session.agent_type,
                    "created_at": (
                        session.created_at.isoformat() if session.created_at else None
                    ),
                    "updated_at": (
                        session.updated_at.isoformat() if session.updated_at else None
                    ),
                    "last_message_at": (
                        session.last_message_at.isoformat()
                        if session.last_message_at
                        else None
                    ),
                }
                for session in sessions
            ], total

    async def get_session_details(
        self, session_id: str, user_id: str
    ) -> Optional[dict]:
        """Get detailed information for a specific session.

        Args:
            session_id: The ID of the session
            user_id: The ID of the user (for authorization)

        Returns:
            A dictionary with session details if found and user has access, None otherwise
        """
        async with get_db() as db:
            result = await db.execute(
                select(Session).where(
                    Session.id == session_id, 
                    Session.user_id == user_id,
                    Session.deleted_at.is_(None)
                )
            )
            session = result.scalar_one_or_none()

            if not session:
                return None

            return {
                "id": str(session.id),
                "user_id": session.user_id,
                "name": session.name,
                "status": session.status,
                "sandbox_id": session.sandbox_id,
                "workspace_dir": f"/workspace/{session.id}",
                "is_public": session.is_public,
                "public_url": session.public_url,
                "token_usage": None,
                "settings": None,
                "agent_type": session.agent_type,
                "created_at": (
                    session.created_at.isoformat() if session.created_at else None
                ),
                "updated_at": (
                    session.updated_at.isoformat() if session.updated_at else None
                ),
                "last_message_at": (
                    session.last_message_at.isoformat()
                    if session.last_message_at
                    else None
                ),
            }

    async def soft_delete_session(self, session_id: str, user_id: str) -> None:
        """Soft delete a session by setting its deleted_at timestamp.

        Args:
            session_id: The ID of the session to delete
            user_id: The ID of the user (for authorization)

        Raises:
            Exception: If session not found or user lacks access
        """
        from datetime import datetime, timezone
        
        async with get_db() as db:
            result = await db.execute(
                select(Session).where(
                    Session.id == session_id, 
                    Session.user_id == user_id,
                    Session.deleted_at.is_(None)
                )
            )
            session = result.scalar_one_or_none()
            
            if not session:
                raise Exception(f"Session {session_id} not found or already deleted")
            
            session.deleted_at = datetime.now(timezone.utc)
            await db.flush()


class EventsTable:
    """Table class for event operations following Open WebUI pattern."""

    async def save_event(
        self, session_id: uuid.UUID, event: RealtimeEvent
    ) -> uuid.UUID:
        """Save an event to the database.

        Args:
            session_id: The UUID of the session this event belongs to
            event: The event to save

        Returns:
            The UUID of the created event
        """
        async with get_db() as db:
            db_event = Event(
                session_id=str(session_id),
                type=event.type.value,
                content=event.content,
            )
            db.add(db_event)
            await db.flush()  # This will populate the id field
            return uuid.UUID(db_event.id)

    async def get_session_events(self, session_id: uuid.UUID) -> list[Event]:
        """Get all events for a session.

        Args:
            session_id: The UUID of the session

        Returns:
            A list of events for the session
        """
        async with get_db() as db:
            result = await db.execute(
                select(Event).where(Event.session_id == str(session_id))
            )
            return result.scalars().all()

    async def delete_session_events(self, session_id: uuid.UUID) -> None:
        """Delete all events for a session.

        Args:
            session_id: The UUID of the session to delete events for
        """
        async with get_db() as db:
            await db.execute(select(Event).where(Event.session_id == str(session_id)))
            # For delete operations, we need to fetch and delete each item
            result = await db.execute(
                select(Event).where(Event.session_id == str(session_id))
            )
            for event in result.scalars():
                await db.delete(event)

    async def delete_events_from_last_to_user_message(
        self, session_id: uuid.UUID
    ) -> None:
        """Delete events from the most recent event backwards to the last user message (inclusive).
        This preserves the conversation history before the last user message.

        Args:
            session_id: The UUID of the session to delete events for
        """
        async with get_db() as db:
            # Find the last user message event
            result = await db.execute(
                select(Event)
                .where(
                    Event.session_id == str(session_id),
                    Event.type == EventType.USER_MESSAGE.value,
                )
                .order_by(Event.timestamp.desc())
            )
            last_user_event = result.scalar_one_or_none()

            if last_user_event:
                # Delete all events after the last user message (inclusive)
                result = await db.execute(
                    select(Event).where(
                        Event.session_id == str(session_id),
                        Event.timestamp >= last_user_event.timestamp,
                    )
                )
                for event in result.scalars():
                    await db.delete(event)
            else:
                # If no user message found, delete all events
                result = await db.execute(
                    select(Event).where(Event.session_id == str(session_id))
                )
                for event in result.scalars():
                    await db.delete(event)

    async def get_session_events_with_details(self, session_id: str) -> List[dict]:
        """Get all events for a specific session ID with session details, sorted by timestamp ascending.

        Args:
            session_id: The session identifier to look up events for

        Returns:
            A list of event dictionaries with their details, sorted by timestamp ascending
        """
        async with get_db() as db:
            result = await db.execute(
                select(Event)
                .where(Event.session_id == session_id)
                .order_by(asc(Event.created_at))
                .options(selectinload(Event.session))
            )
            events = result.scalars().all()

            # Convert events to a list of dictionaries
            event_list = []
            for e in events:
                event_data = {
                    "id": e.id,
                    "session_id": e.session_id,
                    "created_at": e.created_at.isoformat(),
                    "type": e.type,
                    "content": e.content,
                    "workspace_dir": f"/workspace/{e.session.id}",
                }
                event_list.append(event_data)

            return event_list

class FileTable:
    async def get_file_by_id(self, file_id: str):
        async with get_db() as db:
            result = await db.execute(
                select(FileUpload).where(FileUpload.id == file_id)
            )
            return result.scalar_one_or_none()

    async def get_files_by_session_id(self, session_id: str):
        async with get_db() as db:
            result = await db.execute(
                select(FileUpload).where(FileUpload.session_id == session_id)
            )
            return result.scalars().all()

    async def update_session_id(self, file_id: str, session_id: str):
        async with get_db() as db:
            result = await db.execute(
                select(FileUpload).where(FileUpload.id == file_id)
            )
            file = result.scalar_one_or_none()
            if file:
                file.session_id = session_id
                await db.flush()
                return True
            return False

    async def create_file(
        self,
        file_id: str,
        file_name: str,
        file_size: int,
        storage_path: str,
        content_type: str,
        session_id: str,
    ):
        async with get_db() as db:
            # get the user id from the session id
            result = await db.execute(
                select(Session).where(Session.id == session_id)
            )
            session = result.scalar_one_or_none()
            if session:
                user_id = session.user_id
            else:
                raise ValueError(f"Session {session_id} not found")

            db_file = FileUpload(
                id=file_id,
                file_name=file_name,
                file_size=file_size,
                storage_path=storage_path,
                content_type=content_type,
                session_id=session_id,
                user_id=user_id,
            )
            db.add(db_file)
            await db.flush()
            return db_file


# Create singleton instances following Open WebUI pattern
Sessions = SessionsTable()
Events = EventsTable()
Files = FileTable()
