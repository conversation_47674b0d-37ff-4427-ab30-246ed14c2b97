from datetime import datetime, timezone
from typing import Any, List, Optional

from ii_agent.agents.codeact import CodeActAgent
from ii_agent.core.config.agent_config import AgentConfig
from ii_agent.core.config.llm_config import LLMConfig
from ii_agent.core.event_stream import EventStream
from ii_agent.llm.base import LLMClient, ToolParam
from ii_agent.llm.openai import OpenAIDirectClient
from ii_agent.prompts.researcher_system_prompt import ConfigConstants, ResearcherConfig
from ii_agent.sub_agent.base import BaseAgentTool
from ii_agent.llm.context_manager.base import ContextManager
from ii_agent.agents.parser.researcher_parser import DeepResearchMessageParser

from ii_agent.sub_agent.report_builder import ReportBuilder, ReportType
from ii_tool.tools.base import BaseTool, ToolResult

class ResearcherAgent(BaseAgentTool):
    name: str = "sub_agent_researcher"
    display_name: str = "Researcher Agent"
    description: str = (
        "Call this tool whenever user asks for a deep research. An advanced research agent capable of performing deep research on a given instruction. "
        "It synthesizes information, leverages external tools, and generates comprehensive reports "
        "in either 'basic' or 'advanced' formats based on user requirements. After using this tool, "
        "the report must be reformattted in a stunning css, html website that contains all the information in a very readable format before returning to the user."
    )
    input_schema: dict[str, Any] = {
        "type": "object",
        "properties": {
            "instruction": {
                "type": "string",
                "description": "The instruction to perform deep research for",
            },
            "report_type": {
                "type": "string",
                "description": "The type of report to generate. Default to 'basic' unless user specified 'basic' or 'advanced'",
                "enum": ["basic", "advanced"],
            },
        },
        "required": ["instruction"],
    }
    read_only = True

    def __init__(
        self,
        client: LLMClient,
        llm_config: LLMConfig,
        tools: List[BaseTool],
        context_manager: ContextManager,
        event_stream: EventStream,
        max_turns: int = 200,
        config: Optional[Any] = None,
    ):
        tool_params = [
            ToolParam(
                name=tool.name,
                description=tool.description,
                input_schema=tool.input_schema,
            )
            for tool in tools
        ]
        self.report_client = client

        agent_config = AgentConfig(
            stop_sequence=[ConfigConstants.END_CODE],
            temperature=0.6,
            max_tokens_per_turn=10000,
            tools=tool_params
        )

        parser = DeepResearchMessageParser(tools=tool_params)
        researcher_agent = CodeActAgent(
            llm=OpenAIDirectClient(llm_config = llm_config),
            config=agent_config,
            parser=parser,
            is_completion= llm_config.tokenizer is not None,
        )
        super().__init__(
            agent=researcher_agent,
            tools=tools,
            context_manager=context_manager,
            event_stream=event_stream,
            max_turns=max_turns,
            config=config,
        )
    

    async def execute(self, tool_input: dict[str, Any]) -> ToolResult:
        
        instruction = tool_input.get("instruction")
        report_type = tool_input.get("report_type")

        if instruction is None:
            return ToolResult(
                llm_content="Please provide an instruction to perform deep research for",
                user_display_content="Please provide an instruction to perform deep research for",
            )

        if report_type is None:
            return ToolResult(
                llm_content="Please provide a report type",
                user_display_content="Please provide a report type",
            )
        
        system_prompt = ResearcherConfig().system_prompt.format(
                current_date=datetime.now(timezone.utc).isoformat(),
                available_tools=ConfigConstants.AVAILABLE_TOOLS,
        )

        agent_output = await self.controller.run_impl(
            tool_input={
                "instruction": system_prompt + "\n\n" + "The user instruction is: " + str(instruction),
            }
        )

        report_builder = ReportBuilder(
            client=self.report_client
        )
        try:
            report_output = await report_builder.build(
                query=instruction,
                state=self.controller.state,
                    report_type=ReportType(report_type),
                )
            return ToolResult(
                llm_content=report_output,
                user_display_content=report_output
            )
        except Exception as e:
            return ToolResult(
                llm_content=agent_output.llm_content,
                user_display_content=str(e)
            )


    async def execute_mcp_wrapper(
        self,
        description: str,
        prompt: str,
    ):
        return await self._mcp_wrapper(
            tool_input={
                "instruction": prompt,
                "question": prompt,
                "report_type": description,
            }
        )
    