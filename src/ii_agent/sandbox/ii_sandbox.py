from typing import <PERSON><PERSON>, AsyncIterator, Literal, Optional

from ii_sandbox_server.client.client import SandboxClient


class IISandbox:
    """Sandbox instance that communicates with the sandbox server."""

    def __init__(
        self,
        sandbox_id: str,
        sandbox_server_url: str,
        user_id: str,
    ):
        self._sandbox_id = sandbox_id
        self._user_id = user_id
        self.client = SandboxClient(sandbox_server_url)

    @property
    def sandbox_id(self) -> str:
        return self._sandbox_id

    @property
    async def status(self) -> str:
        """Get the status of the sandbox."""
        response = await self.client.get_sandbox_status(self.sandbox_id)
        return response.status

    async def create(self, sandbox_template_id: str | None = None):
        """Create a new sandbox."""
        self._sandbox_id = await self.client.create_sandbox(self._user_id, sandbox_template_id)

    async def connect(self):
        """Connect to a sandbox. If the sandbox is paused, it will be resumed."""
        await self.client.connect_sandbox(self.sandbox_id)

    async def expose_port(self, port: int) -> str:
        """Expose a port in the sandbox."""
        url = await self.client.expose_port(self.sandbox_id, port)
        return url

    async def schedule_timeout(self, timeout_seconds: int):
        """Schedule a timeout for the sandbox."""
        await self.client.schedule_timeout(self.sandbox_id, timeout_seconds)

    async def upload_file(self, file_content: str | bytes | IO, remote_file_path: str):
        """Upload a file to the sandbox."""
        if isinstance(file_content, IO):
            content = file_content.read()
            if isinstance(content, bytes):
                content = content.decode("utf-8")
        else:
            content = (
                file_content
                if not isinstance(file_content, bytes)
                else file_content.decode("utf-8")
            )

        await self.client.write_file(self.sandbox_id, remote_file_path, content)

    async def download_file(
        self, remote_file_path: str, format: Literal["text", "bytes", "stream"] = "text"
    ) -> Optional[str | bytes | AsyncIterator[bytes]]:
        """Download a file from the sandbox."""
        content = await self.client.download_file(
            self.sandbox_id, remote_file_path, format
        )
        return content

    async def write_file(self, file_content: str | bytes | IO, file_path: str) -> bool:
        """Write content to a file in the sandbox."""
        # Convert IO to string or bytes
        if isinstance(file_content, IO):
            content = file_content.read()
            if isinstance(content, bytes):
                content = content.decode("utf-8")
        else:
            content = (
                file_content
                if not isinstance(file_content, bytes)
                else file_content.decode("utf-8")
            )

        response = await self.client.write_file(self.sandbox_id, file_path, content)
        return response.success

    async def read_file(self, file_path: str) -> str | bytes | AsyncIterator[bytes]:
        """Read a file from the sandbox."""
        response = await self.client.read_file(self.sandbox_id, file_path)
        return response.content or ""

    async def create_directory(
        self, directory_path: str, exist_ok: bool = False
    ) -> bool:
        """Create a directory in the sandbox."""
        await self.client.create_directory(self.sandbox_id, directory_path, exist_ok)
        return True
