import json
from typing import <PERSON><PERSON>, <PERSON><PERSON>, Dict, Any
from ii_agent.llm.base import (
    GeneralContentBlock,
    LLMClient,
    TextPrompt,
    TextResult,
    ToolFormattedResult,
)
from ii_agent.llm.context_manager.base import ContextManager
from ii_agent.llm.token_counter import <PERSON><PERSON><PERSON>ounter
from ii_agent.utils.constants import (
    TOKEN_BUDGET,
    SUMMARY_MAX_TOKENS,
    COMPRESSION_TOKEN_THRESHOLD,
)
from ii_agent.core.logger import logger


class LLMCompact(ContextManager):
    def __init__(
        self,
        client: LLMClient,
        token_counter: TokenCounter,
        token_budget: int = TOKEN_BUDGET,
        compression_token_threshold: float = COMPRESSION_TOKEN_THRESHOLD,
    ):
        super().__init__(token_counter=token_counter, token_budget=token_budget)
        self.client = client
        self.token_counter = token_counter
        self.compression_token_threshold = compression_token_threshold

    def _find_index_after_fraction(
        self, message_lists: list[list[GeneralContentBlock]], fraction: float
    ) -> int:
        """
        Returns the index of the message list after the fraction of the total characters.
        Based on Gemini CLI implementation.
        """
        if fraction <= 0 or fraction >= 1:
            raise ValueError("Fraction must be between 0 and 1")

        # Calculate length of each message list
        content_lengths = []
        for message_list in message_lists:
            list_length = 0
            for message in message_list:
                # Convert message to string representation for length calculation
                if isinstance(message, (TextPrompt, TextResult)):
                    list_length += len(message.text)
                elif isinstance(message, ToolFormattedResult):
                    list_length += len(message.tool_output)
                else:
                    list_length += len(json.dumps(message.model_dump()))
            content_lengths.append(list_length)

        total_characters = sum(content_lengths)
        target_characters = total_characters * fraction

        characters_so_far = 0
        for i, length in enumerate(content_lengths):
            characters_so_far += length
            if characters_so_far >= target_characters:
                return i

        return len(content_lengths)

    def _find_turn_boundary(
        self, message_lists: list[list[GeneralContentBlock]], start_index: int
    ) -> int:
        """
        Find the first user message after the given index.
        This ensures we split at a clean turn boundary.
        """
        from ii_agent.llm.base import ImageBlock
        
        index = start_index
        # Skip until we find a message list that starts with a user message
        while index < len(message_lists):
            if message_lists[index]:
                first_message = message_lists[index][0]
                # User messages are TextPrompt, ToolFormattedResult, or ImageBlock
                # Any of these can start a turn
                if isinstance(first_message, (TextPrompt, ToolFormattedResult, ImageBlock)):
                    return index + 1
            index += 1
        return index

    def _try_compress_chat(
        self, message_lists: list[list[GeneralContentBlock]]
    ) -> Optional[Dict[str, Any]]:
        """
        Try to compress the conversation history if it exceeds the threshold.
        Returns compression info with tokens before/after, or None if not compressed.
        """
        # Don't compress empty history
        if not message_lists or len(message_lists) <= 1:
            return None

        # Count current tokens
        original_token_count = self.count_tokens(message_lists)
        
        # Find split point - preserve recent conversation
        compress_before_index = self._find_index_after_fraction(
            message_lists, self.compression_token_threshold
        )
        
        # Find clean turn boundary
        compress_before_index = self._find_turn_boundary(
            message_lists, compress_before_index
        )
        
        # If we can't find a good split point, summarize the whole conversation
        if compress_before_index >= len(message_lists) - 1:
            logger.info("No suitable compression point found, summarizing entire conversation")
            history_to_compress = message_lists
            history_to_keep = []
        else:
            # Split history at the found boundary
            history_to_compress = message_lists[:compress_before_index]
            history_to_keep = message_lists[compress_before_index:]
# Generate summary of history to compress
        summary_text = self._generate_compression_summary(history_to_compress)
        
        # Create new message list with summary
        summary_user_message = [TextPrompt(text=f"You’re working on a long horizon task for me; below is the context you previously wrote summarizing progress so far. {summary_text}.")]
        
        # Combine summary with preserved history
        compressed_messages = [summary_user_message] + history_to_keep
        
        # Count new tokens
        new_token_count = self.count_tokens(compressed_messages)
        
        logger.info(
            f"Compressed history from {original_token_count} to {new_token_count} tokens "
            f"(saved {original_token_count - new_token_count} tokens, "
            f"{100 * (1 - new_token_count/original_token_count):.1f}% reduction)"
        )
        
        return compressed_messages

    def _generate_summary_with_llm(
        self, 
        message_lists: list[list[GeneralContentBlock]], 
        prompt: str, 
        system_prompt: str,
    ) -> str:
        """Generate a summary using the LLM with the given prompt."""
        summary_request = [TextPrompt(text=prompt)]
        messages_for_summary = message_lists + [summary_request]
        model_response, _ = self.client.generate(
            messages=messages_for_summary,
            max_tokens=SUMMARY_MAX_TOKENS,
            system_prompt=system_prompt,
        )
        summary_text = ""
        for message in model_response:
            if isinstance(message, TextResult):
                summary_text += message.text
        return summary_text

    def _generate_compression_summary(
        self, message_lists: list[list[GeneralContentBlock]]
    ) -> str:
        """Generate a structured XML summary of the conversation to compress."""
        return self._generate_summary_with_llm(
            message_lists=message_lists,
            prompt=COMPACT_PROMPT,
            system_prompt="You are a helpful AI assistant tasked with creating structured conversation summaries.",
        )

    def should_truncate(self, message_lists: list[list[GeneralContentBlock]]) -> bool:
        """Check if truncation is needed based on configurable threshold."""
        current_tokens = self.count_tokens(message_lists)
        return current_tokens > self._token_budget

    def apply_truncation(
        self, message_lists: list[list[GeneralContentBlock]]
    ) -> list[list[GeneralContentBlock]]:
        """Apply smart truncation using compression when needed."""
        compression_result = self._try_compress_chat(message_lists)
        return compression_result
    

# Compression prompt for structured XML summaries
COMPRESSION_PROMPT = """
You are tasked with creating a structured summary of the conversation history.
When the conversation grows too large, you compress it into a concise XML snapshot.
This snapshot is CRITICAL as it becomes the agent's only memory of the past.

First, analyze the conversation in a <scratchpad> section:
- Review the user's overall goals and requests
- Identify key technical decisions and patterns
- Note file modifications and system changes
- Track problems solved and ongoing issues
- Identify pending tasks and current work

Then generate a <state_snapshot> with these sections:

<scratchpad>
[Your analysis of the conversation history]
</scratchpad>

<state_snapshot>
    <overall_goal>
        [Single concise sentence describing the user's high-level objective]
    </overall_goal>
    
    <key_knowledge>
        [Bullet points of crucial facts, conventions, and constraints]
        - Technical stack and frameworks being used
        - Important configuration or setup details
        - Key decisions made during the conversation
        - User preferences and requirements
    </key_knowledge>
    
    <file_system_state>
        [List of files created/modified/deleted with their status]
        - Current working directory
        - Files read and key findings
        - Files modified and nature of changes
        - Files created and their purpose
    </file_system_state>
    
    <recent_actions>
        [Summary of last few significant actions and outcomes]
        - Commands executed and results
        - Tests run and their status
        - Debugging steps taken
        - Solutions implemented
    </recent_actions>
    
    <current_plan>
        [Step-by-step plan with completion status]
        1. [DONE] Completed tasks
        2. [IN PROGRESS] Current task being worked on
        3. [TODO] Remaining tasks
    </current_plan>
    
    <errors_and_warnings>
        [Any unresolved errors or important warnings]
        - Error messages encountered
        - Potential issues identified
        - Warnings to keep in mind
    </errors_and_warnings>
    
    <next_step>
        [Immediate next action to take]
        What specific task should be tackled next based on the plan
    </next_step>
</state_snapshot>

Be extremely dense with information. Include specific file paths, function names,
error messages, and technical details. This summary is the ONLY context that will
be preserved, so ensure nothing critical is lost.
"""

COMPACT_USER_MESSAGE = "Use the /compact command to clear the conversation history, and start a new conversation with the summary in context."


COMPACT_PROMPT = """
Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.
This summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing development work without losing context.

Before providing your final summary, wrap your analysis in <analysis> tags to organize your thoughts and ensure you've covered all necessary points. In your analysis process:

1. Chronologically analyze each message and section of the conversation. For each section thoroughly identify:
   - The user's explicit requests and intents
   - Your approach to addressing the user's requests
   - Key decisions, technical concepts and code patterns
   - Specific details like file names, full code snippets, function signatures, file edits, etc
2. Double-check for technical accuracy and completeness, addressing each required element thoroughly.

Your summary should include the following sections:

1. Primary Request and Intent: Capture all of the user's explicit requests and intents in detail
2. Key Technical Concepts: List all important technical concepts, technologies, and frameworks discussed.
3. Files and Code Sections: Enumerate specific files and code sections examined, modified, or created. Pay special attention to the most recent messages and include full code snippets where applicable and include a summary of why this file read or edit is important.
4. Problem Solving: Document problems solved and any ongoing troubleshooting efforts.
5. Pending Tasks: Outline any pending tasks that you have explicitly been asked to work on.
6. Current Work: Describe in detail precisely what was being worked on immediately before this summary request, paying special attention to the most recent messages from both user and assistant. Include file names and code snippets where applicable.
7. Optional Next Step: List the next step that you will take that is related to the most recent work you were doing. IMPORTANT: ensure that this step is DIRECTLY in line with the user's explicit requests, and the task you were working on immediately before this summary request. If your last task was concluded, then only list next steps if they are explicitly in line with the users request. Do not start on tangential requests without confirming with the user first.
                       If there is a next step, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no drift in task interpretation.

Here's an example of how your output should be structured:

<example>
<analysis>
[Your thought process, ensuring all points are covered thoroughly and accurately]
</analysis>

<summary>
1. Primary Request and Intent:
   [Detailed description]

2. Key Technical Concepts:
   - [Concept 1]
   - [Concept 2]
   - [...]

3. Files and Code Sections:
   - [File Name 1]
      - [Summary of why this file is important]
      - [Summary of the changes made to this file, if any]
      - [Important Code Snippet]
   - [File Name 2]
      - [Important Code Snippet]
   - [...]

4. Problem Solving:
   [Description of solved problems and ongoing troubleshooting]

5. Pending Tasks:
   - [Task 1]
   - [Task 2]
   - [...]

6. Current Work:
   [Precise description of current work]

7. Optional Next Step:
   [Optional Next step to take]

</summary>
</example>

Please provide your summary based on the conversation so far, following this structure and ensuring precision and thoroughness in your response. 

There may be additional summarization instructions provided in the included context. If so, remember to follow these instructions when creating the above summary. Examples of instructions include:
<example>
## Compact Instructions
When summarizing the conversation focus on typescript code changes and also remember the mistakes you made and how you fixed them.
</example>

<example>
# Summary instructions
When you are using compact - please focus on test output and code changes. Include file reads verbatim.
</example>
"""