"""Slide management API endpoints."""

from fastapi import APIRouter, Depends, Query

from ii_agent.server.api.deps import get_db_session, DBSession
from ii_agent.server.auth.middleware import CurrentUser
from ii_agent.server.slides.models import (
    SlideWriteRequest,
    SlideWriteResponse,
    PresentationListResponse,
)
from ii_agent.server.slides import service

router = APIRouter(prefix="/slides", tags=["Slide Management"])


@router.post("", response_model=SlideWriteResponse)
async def write_slide(
    write_request: SlideWriteRequest,
    current_user: CurrentUser,
    session_id: str = Query(..., description="Session ID"),
    db: DBSession = Depends(get_db_session),
):
    """Create or overwrite slide content. Updates filesystem and database."""

    result = await service.execute_slide_write(
        db_session=db,
        write_request=write_request,
        session_id=session_id,
        user_id=current_user.id,
    )

    return result


@router.get("", response_model=PresentationListResponse)
async def list_presentations(
    current_user: CurrentUser,
    session_id: str = Query(..., description="Session ID"),
    db: DBSession = Depends(get_db_session),
):
    """Get list of presentations in session from database."""

    result = await service.get_session_presentations(
        db_session=db,
        session_id=session_id,
        user_id=current_user.id,
    )

    return result
