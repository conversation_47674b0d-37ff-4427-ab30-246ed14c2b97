import asyncio
import logging
import uuid
from typing import Optional, List, Callable

import socketio

from ii_agent.controller.agent_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ii_agent.core.event import Agent<PERSON><PERSON><PERSON>, RealtimeEvent, EventType
from ii_agent.core.event_stream import AsyncEventStream
from ii_agent.core.config.ii_agent_config import IIAgentConfig
from ii_agent.storage import BaseStorage
from ii_agent.utils.workspace_manager import WorkspaceManager
from ii_agent.sandbox.ii_sandbox import IISandbox


logger = logging.getLogger(__name__)


class ChatSession:
    """Manages a single standalone chat session with its own agent, workspace, and message handling."""

    def __init__(
        self,
        sockets: List[str],
        workspace_manager: WorkspaceManager,
        session_uuid: uuid.UUID,
        file_store: BaseStorage,
        config: IIAgentConfig,
        sio: socketio.AsyncServer,
        user_id: Optional[str] = None,
    ):
        self.sockets = sockets
        self.workspace_manager = workspace_manager
        self.session_uuid = session_uuid
        self.file_store = file_store
        self.config = config
        self.sio = sio
        self.user_id = user_id

        # Create event stream for this session
        self.event_stream = AsyncEventStream(logger=logger)

        # Session state
        self.agent_controller: Optional[AgentController] = None
        self.reviewer_controller: Optional[AgentController] = None
        self.active_task: Optional[asyncio.Task] = None
        self.first_message = True
        self.enable_reviewer = False
        self.session_created = False
        self.sandbox: Optional[IISandbox] = None
        self.chat_loop_lock = asyncio.Lock()

    def get_sandbox(self) -> IISandbox:
        if not self.sandbox:
            raise ValueError("Sandbox not initialized")
        return self.sandbox

    def setup_subscribers(self, callbacks: List[Callable]):
        for callback in callbacks:
            self.event_stream.subscribe(callback)

    def get_event_stream(self) -> AsyncEventStream:
        """Get the event stream for this session."""
        return self.event_stream

    def save_session_state(self):
        """Save the current session state to file store."""
        if (
            self.agent_controller
            and hasattr(self.agent_controller, "state")
            and self.agent_controller.state
        ):
            self.agent_controller.state.save_to_session(
                str(self.session_uuid), self.file_store
            )
            logger.info(f"Saved session state for session {self.session_uuid}")
        else:
            logger.debug(f"No agent state to save for session {self.session_uuid}")

    def add_socket(self, sid: str):
        """Add a new socket to the broadcast list."""
        if sid not in self.sockets:
            self.sockets.append(sid)

    def remove_socket(self, sid: str):
        """Remove a socket from the broadcast list."""
        if sid in self.sockets:
            self.sockets.remove(sid)

    async def send_event(self, event: RealtimeEvent):
        """Send an event to all clients connected to this session via the event stream."""
        # Use the event stream instead of direct Socket.IO sends to avoid race conditions
        # The SocketIOSubscriber will handle the actual sending to the room
        self.event_stream.add_event(event)

    async def handshake(self, is_new_session: bool = False):
        """Handle handshake message."""
        await self.send_event(
            RealtimeEvent(
                type=EventType.CONNECTION_ESTABLISHED,
                content={
                    "message": "Connected to Agent WebSocket Server",
                    "workspace_path": str(self.workspace_manager.root),
                },
            )
        )
        if self.has_active_task() or is_new_session:
            await self.send_event(
                RealtimeEvent(
                    type=EventType.STATUS_UPDATE,
                    content={"status": AgentStatus.RUNNING},
                )
            )

    def has_active_task(self) -> bool:
        """Check if there's an active task for this session."""
        return self.active_task is not None and not self.active_task.done()

    async def cleanup(self):
        """Clean up resources associated with this session."""
        if self.agent_controller:
            self.agent_controller.cancel()

        # Cancel any running tasks
        if self.active_task and not self.active_task.done():
            self.active_task.cancel()
            self.active_task = None

        # Clean up event stream subscribers
        if hasattr(self, "socketio_subscriber"):
            self.event_stream.unsubscribe(self.socketio_subscriber.handle_event)
        if hasattr(self, "database_subscriber"):
            self.event_stream.unsubscribe(self.database_subscriber.handle_event)

        # Clean up references
        self.sockets.clear()
        self.agent_controller = None
        self.reviewer_controller = None
        self.event_stream = None
