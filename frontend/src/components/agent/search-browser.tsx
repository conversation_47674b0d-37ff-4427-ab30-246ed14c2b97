import React from 'react'

interface SearchBrowserProps {
    className?: string
    keyword?: string
    search_results?: string | Record<string, unknown> | undefined
}

const SearchBrowser = React.memo(
    ({ className, keyword, search_results }: SearchBrowserProps) => {
        if (!keyword) return

        return (
            <div
                className={`flex-1 bg-grey dark:bg-black px-4 divide-y divide-grey-2/30 dark:divide-white/30 overflow-auto ${className}`}
            >
                <div className="space-y-3 py-4">
                    {Array.isArray(search_results) &&
                        search_results?.length > 0 && (
                            <p className="text-sm font-bold">{`${search_results?.length} Results`}</p>
                        )}
                    {Array.isArray(search_results) &&
                        search_results?.map((item, index) => (
                            <div
                                key={index}
                                className="flex flex-col py-3 px-4 bg-firefly dark:bg-sky-blue rounded-xl shadow-btn"
                            >
                                <a
                                    href={item.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="font-semibold text-white dark:text-black hover:underline text-xs line-clamp-1"
                                >
                                    {item.url}
                                </a>
                                <span className="text-white dark:text-black text-xs line-clamp-1 break-all">
                                    {item.title}
                                </span>
                            </div>
                        ))}
                </div>
            </div>
        )
    }
)

SearchBrowser.displayName = 'SearchBrowser'

export default SearchBrowser
