'use client'

import { motion } from 'framer-motion'
import {
    <PERSON>,
    Pencil,
    // <PERSON><PERSON><PERSON>or<PERSON>,
    SearchCheck,
    Folder,
    ChevronDown,
    ChevronRight
} from 'lucide-react'
import { useState, useEffect, useCallback, useMemo, memo } from 'react'
// import { FixedSizeList as List } from 'react-window' // For future virtualization

import Action from './action'
import Markdown from '@/components/markdown'
import QuestionInput from '@/components/question-input'
import { ActionStep, BUILD_STEP, Message, TAB } from '@/typings/agent'
import { Button } from '../ui/button'
import EditQuestion from './edit-question'
import {
    selectCurrentQuestion,
    selectEditingMessage,
    selectIsCompleted,
    selectIsLoading,
    selectIsStopped,
    selectMessages,
    selectToolSettings,
    selectWorkspaceInfo,
    setActiveTab,
    setSelectedBuildStep,
    useAppDispatch,
    useAppSelector,
    type AppDispatch
} from '@/state'
import { getFileIconAndColor } from '@/utils/file-utils'
import ModelTag from '../model-tag'

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
): T & { cancel: () => void } {
    let timeoutId: NodeJS.Timeout
    const debounced = ((...args: Parameters<T>) => {
        clearTimeout(timeoutId)
        timeoutId = setTimeout(() => func(...args), delay)
    }) as T & { cancel: () => void }

    debounced.cancel = () => clearTimeout(timeoutId)
    return debounced
}

// Memoized message component for better performance
interface MemoizedMessageContentProps {
    message: Message
    isLatestUser: boolean
    editingMessage: Message | null | undefined
    workspaceInfo: string
    isThinkMessageExpanded: (id: string) => boolean
    toggleThinkMessage: (id: string) => void
    handleSetEditingMessage: (msg?: Message) => void
    handleEditMessage: (question: string) => void
    handleClickAction: (
        data: ActionStep | undefined,
        showTabOnly?: boolean
    ) => void
    dispatch: AppDispatch
    isReplayMode: boolean
}

const MemoizedMessageContent = memo(
    ({
        message,
        isLatestUser,
        editingMessage,
        workspaceInfo,
        isThinkMessageExpanded,
        toggleThinkMessage,
        handleSetEditingMessage,
        handleEditMessage,
        handleClickAction,
        dispatch,
        isReplayMode
    }: MemoizedMessageContentProps) => {
        const fileElements = useMemo(() => {
            if (!message.files || message.files.length === 0) return null

            // Process files logic (same as before but memoized)
            const folderFiles = message.files.filter((fileName) =>
                fileName.match(/^folder:(.+):(\d+)$/)
            )

            const folderNames = folderFiles
                .map((folderFile) => {
                    const match = folderFile.match(/^folder:(.+):(\d+)$/)
                    return match ? match[1] : null
                })
                .filter(Boolean) as string[]

            const filesToDisplay = message.files.filter((fileName) => {
                if (fileName.match(/^folder:(.+):(\d+)$/)) {
                    return true
                }
                for (const folderName of folderNames) {
                    if (fileName.includes(folderName)) {
                        return false
                    }
                }
                return true
            })

            return filesToDisplay.map((fileName, fileIndex) => {
                const isFolderMatch = fileName.match(/^folder:(.+):(\d+)$/)
                if (isFolderMatch) {
                    const folderName = isFolderMatch[1]
                    const fileCount = parseInt(isFolderMatch[2], 10)

                    return (
                        <div
                            key={`${message.id}-folder-${fileIndex}`}
                            className="inline-block ml-auto bg-[#35363a] text-white rounded-2xl px-4 py-3 border border-gray-700 shadow-sm"
                        >
                            <div className="flex items-center gap-3">
                                <div className="flex items-center justify-center w-12 h-12 bg-blue-600 rounded-xl">
                                    <Folder className="size-6 text-white" />
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-base font-medium">
                                        {folderName}
                                    </span>
                                    <span className="text-left text-sm text-gray-500">
                                        {fileCount}{' '}
                                        {fileCount === 1 ? 'file' : 'files'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    )
                }

                const isImage =
                    fileName.match(
                        /\.(jpeg|jpg|gif|png|webp|svg|heic|bmp)$/i
                    ) !== null

                if (
                    isImage &&
                    message.fileContents &&
                    message.fileContents[fileName]
                ) {
                    return (
                        <div
                            key={`${message.id}-file-${fileIndex}`}
                            className="inline-block ml-auto rounded-3xl overflow-hidden max-w-[320px]"
                        >
                            <div className="w-40 h-40 rounded-xl overflow-hidden">
                                <img
                                    src={message.fileContents[fileName]}
                                    alt={fileName}
                                    className="w-full h-full object-cover"
                                    loading="lazy"
                                />
                            </div>
                        </div>
                    )
                }

                const { IconComponent, bgColor, label } =
                    getFileIconAndColor(fileName)

                return (
                    <div
                        key={`${message.id}-file-${fileIndex}`}
                        className="inline-block ml-auto bg-[#35363a] text-white rounded-2xl px-4 py-3 border border-gray-700 shadow-sm"
                    >
                        <div className="flex items-center gap-3">
                            <div
                                className={`flex items-center justify-center w-12 h-12 ${bgColor} rounded-xl`}
                            >
                                <IconComponent className="size-6 text-white" />
                            </div>
                            <div className="flex flex-col">
                                <span className="text-base font-medium">
                                    {fileName}
                                </span>
                                <span className="text-left text-sm text-gray-500">
                                    {label}
                                </span>
                            </div>
                        </div>
                    </div>
                )
            })
        }, [message.files, message.fileContents, message.id])

        return (
            <>
                {fileElements && (
                    <div className="flex flex-col gap-2 mb-2">
                        {fileElements}
                    </div>
                )}
                {message.content && (
                    <div
                        className={`inline-block text-left rounded-lg ${
                            message.role === 'user'
                                ? 'bg-[#f5f5f5] dark:bg-grey p-3 max-w-[80%] text-black whitespace-pre-wrap border border-grey dark:none'
                                : 'text-white w-full'
                        } ${
                            editingMessage?.id === message.id
                                ? 'w-full max-w-none'
                                : ''
                        } ${
                            message.content?.startsWith('```Thinking:')
                                ? 'agent-thinking w-full'
                                : ''
                        }`}
                    >
                        {message.role === 'user' ? (
                            <div>
                                {editingMessage?.id === message.id ? (
                                    <EditQuestion
                                        editingMessage={message.content}
                                        handleCancel={() =>
                                            handleSetEditingMessage(undefined)
                                        }
                                        handleEditMessage={handleEditMessage}
                                    />
                                ) : (
                                    <div className="relative group">
                                        <div className="text-left text-sm">
                                            {message.content}
                                        </div>
                                        {isLatestUser && !isReplayMode && (
                                            <div className="absolute -bottom-[45px] -right-[20px] opacity-0 group-hover:opacity-100 transition-opacity">
                                                <Button
                                                    variant="ghost"
                                                    size="icon"
                                                    className="text-xs cursor-pointer hover:!bg-transparent"
                                                    onClick={() =>
                                                        handleSetEditingMessage(
                                                            message
                                                        )
                                                    }
                                                >
                                                    <Pencil className="size-3 mr-1" />
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        ) : message?.isThinkMessage ? (
                            <div
                                className={`inline-flex flex-col bg-[#f5f5f5] dark:bg-sky-blue/[0.18] border border-grey rounded-xl overflow-hidden ${
                                    isThinkMessageExpanded(message.id)
                                        ? 'w-full'
                                        : ''
                                }`}
                            >
                                <button
                                    onClick={() =>
                                        toggleThinkMessage(message.id)
                                    }
                                    className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-black/5 dark:hover:bg-white/5 transition-colors"
                                >
                                    <div className="flex items-center gap-2">
                                        <div className="w-2 h-2 bg-firefly dark:bg-sky-blue rounded-full"></div>
                                        <span className="font-medium text-sm text-gray-700 dark:text-gray-300">
                                            Thought
                                        </span>
                                    </div>
                                    {isThinkMessageExpanded(message.id) ? (
                                        <ChevronDown className="size-4 text-gray-500 dark:text-gray-400" />
                                    ) : (
                                        <ChevronRight className="size-4 text-gray-500 dark:text-gray-400" />
                                    )}
                                </button>
                                {isThinkMessageExpanded(message.id) && (
                                    <div className="px-4 pb-4">
                                        <Markdown>
                                            {message.content?.replace(
                                                '<video>',
                                                '&lt;video&gt;'
                                            )}
                                        </Markdown>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div>
                                <Markdown>
                                    {message.content?.replace(
                                        '<video>',
                                        '&lt;video&gt;'
                                    )}
                                </Markdown>
                            </div>
                        )}
                    </div>
                )}
                {message.action && (
                    <div className="mt-2">
                        <Action
                            workspaceInfo={workspaceInfo}
                            type={message.action.type}
                            value={message.action.data}
                            onClick={() => {
                                dispatch(setActiveTab(TAB.BUILD))
                                dispatch(setSelectedBuildStep(BUILD_STEP.BUILD))
                                handleClickAction(message.action, true)
                            }}
                        />
                    </div>
                )}
            </>
        )
    },
    (prevProps, nextProps) => {
        return (
            prevProps.message.id === nextProps.message.id &&
            prevProps.message.content === nextProps.message.content &&
            prevProps.isLatestUser === nextProps.isLatestUser &&
            prevProps.editingMessage?.id === nextProps.editingMessage?.id
        )
    }
)

MemoizedMessageContent.displayName = 'MemoizedMessageContent'

interface ChatMessageProps {
    isReplayMode: boolean
    messagesEndRef: React.RefObject<HTMLDivElement | null>
    handleClickAction: (
        data: ActionStep | undefined,
        showTabOnly?: boolean
    ) => void
    setCurrentQuestion: (value: string) => void
    handleKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void
    handleQuestionSubmit: (question: string) => void
    handleEnhancePrompt: () => void
    handleCancel: () => void
    handleEditMessage: (newQuestion: string) => void
    processAllEventsImmediately?: () => void
    connectWebSocket: () => void
    handleReviewSession: () => void
}

const ChatMessage = ({
    messagesEndRef,
    isReplayMode,
    handleClickAction,
    setCurrentQuestion,
    handleKeyDown,
    handleQuestionSubmit,
    handleEnhancePrompt,
    handleCancel,
    handleEditMessage,
    // processAllEventsImmediately,
    connectWebSocket,
    handleReviewSession
}: ChatMessageProps) => {
    const dispatch = useAppDispatch()
    const [showQuestionInput, setShowQuestionInput] = useState(false)
    const [userHasScrolledUp, setUserHasScrolledUp] = useState(false)
    const [manuallyCollapsedThinkMessages, setManuallyCollapsedThinkMessages] =
        useState<Set<string>>(new Set())
    // const [listHeight, setListHeight] = useState(600) // For future virtualization

    const currentQuestion = useAppSelector(selectCurrentQuestion)
    const isLoading = useAppSelector(selectIsLoading)
    const messages = useAppSelector(selectMessages)
    const isCompleted = useAppSelector(selectIsCompleted)
    const toolSettings = useAppSelector(selectToolSettings)
    const editingMessage = useAppSelector(selectEditingMessage)
    const isStopped = useAppSelector(selectIsStopped)
    const workspaceInfo = useAppSelector(selectWorkspaceInfo)

    useEffect(() => {
        if (isReplayMode && !isLoading && messages.length > 0) {
            // If we're in replay mode, loading is complete, and we have messages,
            // we can assume all events have been processed
            setShowQuestionInput(true)
        }
    }, [isReplayMode, isLoading, messages.length])

    // Add scroll event listener to detect manual scrolling with debouncing
    const debouncedHandleScroll = useCallback(
        debounce(() => {
            const messagesContainer = messagesEndRef.current?.parentElement
            if (!messagesContainer) return

            const isAtBottom =
                messagesContainer.scrollHeight -
                    messagesContainer.scrollTop -
                    messagesContainer.clientHeight <
                50
            setUserHasScrolledUp(!isAtBottom)
        }, 100),
        [messagesEndRef]
    )

    useEffect(() => {
        const messagesContainer = messagesEndRef.current?.parentElement
        if (!messagesContainer) return

        messagesContainer.addEventListener('scroll', debouncedHandleScroll)
        return () => {
            messagesContainer.removeEventListener(
                'scroll',
                debouncedHandleScroll
            )
            debouncedHandleScroll.cancel()
        }
    }, [messagesEndRef, debouncedHandleScroll])

    // Future: Update list height based on container size for virtualization
    // useEffect(() => {
    //     const updateHeight = () => {
    //         const container = messagesEndRef.current?.parentElement
    //         if (container) {
    //             setListHeight(container.clientHeight - 100)
    //         }
    //     }
    //     updateHeight()
    //     window.addEventListener('resize', updateHeight)
    //     return () => window.removeEventListener('resize', updateHeight)
    // }, [])

    // Replace the existing useEffect for message changes
    useEffect(() => {
        if (messages.length > 0 && !userHasScrolledUp) {
            setTimeout(() => {
                messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
            }, 100)
        }
    }, [messages?.length, userHasScrolledUp])

    // const handleJumpToResult = () => {
    //     if (processAllEventsImmediately) {
    //         processAllEventsImmediately()
    //     }

    //     setTimeout(() => {
    //         messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
    //         setShowQuestionInput(true)
    //     }, 100)
    // }

    // Memoize latest user message
    const latestUserMessage = useMemo(() => {
        const userMessages = messages.filter((msg) => msg.role === 'user')
        return userMessages.length > 0
            ? userMessages[userMessages.length - 1]
            : undefined
    }, [messages])

    // Helper function to check if a message is the latest user message
    const isLatestUserMessage = useCallback(
        (message: Message): boolean => {
            return latestUserMessage?.id === message.id
        },
        [latestUserMessage]
    )

    const handleSetEditingMessage = (message?: Message) => {
        dispatch({ type: 'SET_EDITING_MESSAGE', payload: message })
    }

    const isThinkMessageExpanded = useCallback(
        (messageId: string): boolean => {
            // If manually collapsed, show collapsed
            if (manuallyCollapsedThinkMessages.has(messageId)) {
                return false
            }

            // Default behavior: always expanded for think messages
            return true
        },
        [manuallyCollapsedThinkMessages]
    )

    const toggleThinkMessage = useCallback(
        (messageId: string) => {
            const isCurrentlyExpanded = isThinkMessageExpanded(messageId)

            if (isCurrentlyExpanded) {
                // If currently expanded, collapse it manually
                setManuallyCollapsedThinkMessages((prev) =>
                    new Set(prev).add(messageId)
                )
            } else {
                // If currently collapsed, remove from collapsed set to expand
                setManuallyCollapsedThinkMessages((prev) => {
                    const newSet = new Set(prev)
                    newSet.delete(messageId)
                    return newSet
                })
            }
        },
        [isThinkMessageExpanded]
    )

    useEffect(() => {
        if (isReplayMode && showQuestionInput) {
            connectWebSocket()
        }
    }, [isReplayMode, showQuestionInput])

    // Memoize expensive computations
    const visibleMessages = useMemo(
        () => messages.filter((msg) => !msg.isHidden),
        [messages]
    )

    return (
        <div className="h-full flex flex-col">
            <div className="pb-4 px-4 w-full flex-1 overflow-y-auto overflow-x-hidden relative">
                {visibleMessages.map((message) => (
                    <div
                        key={message.id}
                        className={`mb-4 ${
                            message.role === 'user' ? 'text-right' : 'text-left'
                        } ${message.role === 'user' && !message.files && 'mb-8'}`}
                    >
                        <MemoizedMessageContent
                            message={message}
                            isLatestUser={isLatestUserMessage(message)}
                            editingMessage={editingMessage}
                            workspaceInfo={workspaceInfo}
                            isThinkMessageExpanded={isThinkMessageExpanded}
                            toggleThinkMessage={toggleThinkMessage}
                            handleSetEditingMessage={handleSetEditingMessage}
                            handleEditMessage={handleEditMessage}
                            handleClickAction={handleClickAction}
                            dispatch={dispatch}
                            isReplayMode={isReplayMode}
                        />
                    </div>
                ))}

                {isCompleted && (
                    <div className="flex flex-col gap-y-4">
                        <div className="flex">
                            <div className="flex gap-x-2 items-center bg-sky-blue-2 text-black text-sm font-bold px-4 py-2 rounded-full">
                                <div className="flex gap-x-2 items-center">
                                    <Check className="size-5" />
                                    <span>
                                        II-Agent has completed the task.
                                    </span>
                                </div>
                            </div>
                        </div>
                        {toolSettings?.enable_reviewer && (
                            <div
                                className={`group cursor-pointer flex items-start gap-2 px-3 py-2 bg-[#35363a] rounded-xl backdrop-blur-sm 
      shadow-sm
      transition-all duration-200 ease-out
      hover:shadow-[0_2px_8px_rgba(0,0,0,0.24)]
      active:scale-[0.98] overflow-hidden
      animate-fadeIn`}
                            >
                                <div className="flex text-sm items-center justify-between flex-1">
                                    <div className="flex items-center gap-x-1.5 flex-1">
                                        <SearchCheck className="size-5 text-white" />
                                        <span className="text-neutral-100 flex-1 font-medium group-hover:text-white">
                                            Allow II-Agent to review the results
                                        </span>
                                    </div>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="cursor-pointer text-neutral-900 bg-gradient-skyblue-lavender hover:text-neutral-950"
                                        onClick={handleReviewSession}
                                    >
                                        Review
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>
                )}

                {isStopped && !isCompleted && (
                    <div className="flex items-center">
                        <div className="flex items-center gap-x-[6px] py-2 px-4 bg-sky-blue-2 text-black rounded-full">
                            <div className="size-3 bg-black m-1" />
                            <span className="text-sm font-bold">
                                II-Agent has stopped
                            </span>
                        </div>
                    </div>
                )}

                {isLoading && !isStopped && !isCompleted && (
                    <div className="flex items-center gap-x-1.5 text-black/[0.56] dark:text-[#999999] text-sm">
                        <span>II-Agent Thinking</span>
                        <div className="flex gap-x-1">
                            <motion.div
                                className="size-1 bg-[#999999] rounded-full"
                                animate={{
                                    y: [0, -6, 0],
                                    opacity: [0.3, 1, 0.3]
                                }}
                                transition={{
                                    duration: 1.2,
                                    repeat: Infinity,
                                    delay: 0,
                                    ease: 'easeInOut'
                                }}
                            />
                            <motion.div
                                className="size-1 bg-[#999999] rounded-full"
                                animate={{
                                    y: [0, -6, 0],
                                    opacity: [0.3, 1, 0.3]
                                }}
                                transition={{
                                    duration: 1.2,
                                    repeat: Infinity,
                                    delay: 0.15,
                                    ease: 'easeInOut'
                                }}
                            />
                            <motion.div
                                className="size-1 bg-[#999999] rounded-full"
                                animate={{
                                    y: [0, -6, 0],
                                    opacity: [0.3, 1, 0.3]
                                }}
                                transition={{
                                    duration: 1.2,
                                    repeat: Infinity,
                                    delay: 0.3,
                                    ease: 'easeInOut'
                                }}
                            />
                        </div>
                    </div>
                )}

                <div ref={messagesEndRef} />
            </div>
            {isReplayMode ? (
                showQuestionInput ? (
                    <div className="flex flex-col items-start gap-2 px-4">
                        <ModelTag />
                        <QuestionInput
                            hideSuggestions
                            className="w-full max-w-none"
                            textareaClassName="min-h-40 h-40 w-full"
                            placeholder="Ask me anything..."
                            value={currentQuestion}
                            setValue={setCurrentQuestion}
                            handleKeyDown={handleKeyDown}
                            handleSubmit={handleQuestionSubmit}
                            handleEnhancePrompt={handleEnhancePrompt}
                            handleCancel={handleCancel}
                            hideFeatureSelector
                            isDisabled={isLoading}
                        />
                    </div>
                ) : (
                    <motion.div
                        className="sticky bottom-0 left-0 w-full p-4 pb-0"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.2, duration: 0.3 }}
                    >
                        {/* <div className="bg-white/5 backdrop-blur-md border dark:border-white/10 rounded-2xl p-3 flex items-center justify-between">
                            <div className="flex items-center gap-2 ml-2">
                                <div className="animate-pulse">
                                    <div className="h-2 w-2 bg-black dark:bg-white rounded-full"></div>
                                </div>
                                <span className="dark:text-white">
                                    II-Agent is replaying the task...
                                </span>
                            </div>
                            <div className="flex gap-2">
                                <Button
                                    variant="outline"
                                    className="cursor-pointer rounded-full"
                                    onClick={handleJumpToResult}
                                >
                                    <SkipForward /> Skip to results
                                </Button>
                            </div>
                        </div> */}
                    </motion.div>
                )
            ) : (
                <motion.div
                    className="sticky bottom-0 left-0 w-full px-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.3 }}
                >
                    <QuestionInput
                        hideSuggestions
                        className="w-full max-w-none"
                        textareaClassName="min-h-40 h-40 w-full"
                        placeholder="Ask me anything..."
                        value={currentQuestion}
                        setValue={setCurrentQuestion}
                        handleKeyDown={handleKeyDown}
                        handleSubmit={handleQuestionSubmit}
                        handleEnhancePrompt={handleEnhancePrompt}
                        handleCancel={handleCancel}
                        isDisabled={isLoading}
                        hideFeatureSelector
                    />
                </motion.div>
            )}
        </div>
    )
}

export default memo(ChatMessage, (prevProps, nextProps) => {
    // Custom comparison for better performance - avoid JSON.stringify
    return (
        prevProps.isReplayMode === nextProps.isReplayMode &&
        prevProps.messagesEndRef === nextProps.messagesEndRef &&
        prevProps.handleClickAction === nextProps.handleClickAction &&
        prevProps.handleQuestionSubmit === nextProps.handleQuestionSubmit &&
        prevProps.handleEditMessage === nextProps.handleEditMessage
    )
})
