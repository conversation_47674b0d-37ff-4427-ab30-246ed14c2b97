import {
    useState,
    useEffect,
    useRef,
    forwardRef,
    useImperativeHandle
} from 'react'

interface EditableHtmlRendererProps {
    htmlContent: string
    onContentChange?: (
        fullHtmlContent: string,
        changes: Record<string, string>
    ) => void
}

export interface EditableHtmlRendererRef {
    getContainerHeight: () => number
}

export const EditableHtmlRenderer = forwardRef<
    EditableHtmlRendererRef,
    EditableHtmlRendererProps
>(({ htmlContent, onContentChange }, ref) => {
    const [contentState, setContentState] = useState<Record<string, string>>({})
    const containerRef = useRef<HTMLDivElement>(null)

    useImperativeHandle(
        ref,
        () => ({
            getContainerHeight: () => {
                return containerRef.current?.scrollHeight || 720
            }
        }),
        []
    )

    // Function to make HTML elements editable
    const makeElementsEditable = (htmlString: string): string => {
        const parser = new DOMParser()
        const doc = parser.parseFromString(htmlString, 'text/html')

        // Add CSS for editable elements
        const style = doc.createElement('style')
        style.textContent = `
            .editable {
                cursor: pointer;
                border: 2px solid transparent;
                transition: border-color 0.2s ease;
                display: inline-block;
                min-width: 20px;
                min-height: 1em;
            }
            .editable:hover {
                border-color: #181e1c;
                border-style: dashed;
            }
            .editing {
                border-color: #181e1c !important;
                border-style: dashed !important;
                outline: none;
                padding: 2px 4px;
                margin: -2px -4px;
            }
        `
        doc.head.appendChild(style)

        let editId = 1

        // Function to process text nodes and wrap them in editable spans
        const processTextNodes = (element: Element) => {
            const walker = doc.createTreeWalker(element, NodeFilter.SHOW_TEXT, {
                acceptNode: (node) => {
                    // Skip if parent is already editable or is a script/style tag
                    const parent = node.parentElement
                    if (
                        !parent ||
                        parent.classList.contains('editable') ||
                        ['SCRIPT', 'STYLE', 'META', 'LINK', 'TITLE'].includes(
                            parent.tagName
                        )
                    ) {
                        return NodeFilter.FILTER_REJECT
                    }

                    // Only process text nodes with meaningful content
                    const text = node.textContent?.trim()
                    return text && text.length > 0
                        ? NodeFilter.FILTER_ACCEPT
                        : NodeFilter.FILTER_REJECT
                }
            })

            const textNodes: Text[] = []
            let node
            while ((node = walker.nextNode())) {
                textNodes.push(node as Text)
            }

            // Wrap each text node in an editable span
            textNodes.forEach((textNode) => {
                const text = textNode.textContent?.trim()
                if (text && text.length > 0) {
                    const span = doc.createElement('span')
                    span.className = 'editable'
                    const editIdStr = `edit-${editId++}`
                    span.setAttribute('data-edit-id', editIdStr)

                    // Apply saved content if it exists, otherwise use original text
                    const savedContent = contentState[editIdStr]
                    span.textContent =
                        savedContent || textNode.textContent || ''

                    textNode.parentNode?.replaceChild(span, textNode)
                }
            })
        }

        // Process the body content
        if (doc.body) {
            processTextNodes(doc.body)

            // Create a wrapper div that will contain everything
            const wrapper = doc.createElement('div')

            // Convert body to div with all its attributes
            const bodyDiv = doc.createElement('div')

            // Copy all attributes from body to div (including data-slide-id)
            Array.from(doc.body.attributes).forEach((attr) => {
                bodyDiv.setAttribute(attr.name, attr.value)
            })

            // Copy all children from body to bodyDiv
            while (doc.body.firstChild) {
                bodyDiv.appendChild(doc.body.firstChild)
            }

            // Move all head elements (styles, links, meta) to wrapper
            const headElements = doc.head.querySelectorAll('style, link, meta')
            headElements.forEach((element) => {
                wrapper.appendChild(element.cloneNode(true))
            })

            // Add the body content div
            wrapper.appendChild(bodyDiv)

            // Return the wrapper containing styles and body content
            return wrapper.innerHTML
        }

        return doc.documentElement.outerHTML
    }

    useEffect(() => {
        let editingElement: HTMLElement | null = null
        let originalContent = ''

        // Function to reconstruct the full HTML with all changes applied
        const reconstructFullHtml = (
            changes: Record<string, string>
        ): string => {
            if (!containerRef.current) return htmlContent

            // Clone the current container DOM to avoid modifying the live DOM
            const containerClone = containerRef.current.cloneNode(
                true
            ) as HTMLElement

            // Remove editing styles and classes from all editable elements
            const editableElements = containerClone.querySelectorAll('.editable')
            editableElements.forEach((element) => {
                element.classList.remove('editing')
                element.removeAttribute('contenteditable')
                element.removeAttribute('style')
            })

            // Apply all changes to the cloned DOM
            Object.entries(changes).forEach(([editId, newContent]) => {
                const element = containerClone.querySelector(
                    `[data-edit-id="${editId}"]`
                )
                if (element) {
                    element.textContent = newContent
                }
            })

            // Get the innerHTML of the cloned container
            const updatedInnerHTML = containerClone.innerHTML

            // Now we need to reconstruct a full HTML document
            // Parse the original HTML to get the structure
            const parser = new DOMParser()
            const originalDoc = parser.parseFromString(htmlContent, 'text/html')

            // Replace the body content with our updated content
            if (originalDoc.body) {
                originalDoc.body.innerHTML = updatedInnerHTML
            }

            return originalDoc.documentElement.outerHTML
        }

        const saveContent = (
            element: HTMLElement,
            newContent: string,
            originalContent: string
        ) => {
            const elementId = element.getAttribute('data-edit-id')
            if (elementId && newContent !== originalContent) {
                setContentState((prev) => {
                    const updated = {
                        ...prev,
                        [elementId]: newContent
                    }

                    // Reconstruct and provide the full HTML content
                    const fullHtmlContent = reconstructFullHtml(updated)
                    onContentChange?.(fullHtmlContent, updated)

                    return updated
                })
            }
        }

        const handleClick = (event: MouseEvent) => {
            const target = event.target as HTMLElement

            if (target.classList && target.classList.contains('editable')) {
                event.preventDefault()
                event.stopPropagation()

                // If we're switching from one element to another, save the previous one
                if (editingElement && editingElement !== target) {
                    const currentContent = editingElement.innerText || ''
                    saveContent(editingElement, currentContent, originalContent)
                    editingElement.contentEditable = 'false'
                    editingElement.classList.remove('editing')
                }

                // Start editing the new element
                editingElement = target
                originalContent = target.innerText || ''
                target.contentEditable = 'true'
                target.classList.add('editing')
                target.focus()

                // Place cursor at the end without selecting text
                const range = document.createRange()
                const sel = window.getSelection()
                range.selectNodeContents(target)
                range.collapse(false) // Collapse to end
                sel?.removeAllRanges()
                sel?.addRange(range)
            } else if (editingElement && !editingElement.contains(target)) {
                // Clicking outside - save if content changed
                const currentContent = editingElement.innerText || ''
                saveContent(editingElement, currentContent, originalContent)
                editingElement.contentEditable = 'false'
                editingElement.classList.remove('editing')
                editingElement = null
            }
        }

        const handleKeyDown = (event: KeyboardEvent) => {
            if (!editingElement) return

            if (event.key === 'Enter') {
                event.preventDefault()
                const currentContent = editingElement.innerText || ''
                saveContent(editingElement, currentContent, originalContent)
                editingElement.contentEditable = 'false'
                editingElement.classList.remove('editing')
                editingElement = null
            } else if (event.key === 'Escape') {
                event.preventDefault()
                editingElement.innerText = originalContent
                editingElement.contentEditable = 'false'
                editingElement.classList.remove('editing')
                editingElement = null
            }
        }

        const container = containerRef.current
        if (container) {
            container.addEventListener('click', handleClick)
            container.addEventListener('keydown', handleKeyDown)

            return () => {
                container.removeEventListener('click', handleClick)
                container.removeEventListener('keydown', handleKeyDown)
            }
        }
    }, [contentState, onContentChange])

    return (
        <>
            <div
                ref={containerRef}
                dangerouslySetInnerHTML={{
                    __html: makeElementsEditable(htmlContent)
                }}
            />
        </>
    )
})

EditableHtmlRenderer.displayName = 'EditableHtmlRenderer'
